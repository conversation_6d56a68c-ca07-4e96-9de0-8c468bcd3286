import { styled, useAsync } from '@topwrite/common';
import { useState } from 'react';
import { Form, Modal } from 'react-bootstrap';
import { BsChevronDown, BsChevronRight, BsTrash } from 'react-icons/bs';
import { GoPlus } from 'react-icons/go';
import { VscSettingsGear } from 'react-icons/vsc';
import Button from '../../components/button';
import Loader from '../../components/loader';
import useLocalStorageStateWithBook from '../../lib/use-local-storage-state-with-book';
import { useContext } from './context';
import { McpServer } from './use-mcp-servers';

interface ToolsConfigModalProps {
    show: boolean;
    onHide: () => void;
}

export default function ToolsConfigModal({ show, onHide }: ToolsConfigModalProps) {
    const [showAddMcpModal, setShowAddMcpModal] = useState(false);
    const { mcpServers, addMcpServer } = useContext();

    return <>
        <Modal show={show} onHide={onHide} size='lg' scrollable>
            <Modal.Header closeButton>
                <Modal.Title as='h6' className='d-flex align-items-center'>
                    <VscSettingsGear className='me-2' />
                    工具配置
                </Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <ToolsContainer>
                    {/* 内置工具区域 */}
                    <BuiltinTools />
                    {/* MCP 服务器区域 */}
                    {mcpServers.map(server => {
                        return <McpServerItem key={server.name} server={server} />;
                    })}
                </ToolsContainer>
            </Modal.Body>
            <Modal.Footer className='d-flex justify-content-between align-items-center'>
                <Button
                    variant='outline-primary'
                    onClick={() => setShowAddMcpModal(true)}
                    className='d-flex align-items-center'
                >
                    <GoPlus className='me-1' />
                    添加 MCP 服务器
                </Button>
            </Modal.Footer>
        </Modal>

        {/* 添加 MCP 服务器弹窗 */}
        <AddMcpModal
            show={showAddMcpModal}
            onHide={() => setShowAddMcpModal(false)}
            onAdd={addMcpServer}
        />
    </>;
}

const BuiltinTools = () => {
    const { builtinTools: tools } = useContext();
    const [expanded, setExpanded] = useState(true);
    const [disabledTools, setDisabledTools] = useLocalStorageStateWithBook<string[]>('builtin-disabled-tools', []);
    const enabledCount = tools.length - disabledTools.length;

    const toggleAllTools = (enabled: boolean) => {
        if (enabled) {
            // 启用所有工具，清空禁用列表
            setDisabledTools([]);
        } else {
            // 禁用所有工具，将所有工具名称添加到禁用列表
            setDisabledTools(tools.map(tool => `${tool.plugin}.${tool.name}`));
        }
    };

    const toggleTool = (name: string) => {
        if (disabledTools.includes(name)) {
            // 如果当前是禁用状态，则启用（从禁用列表中移除）
            setDisabledTools(disabledTools.filter(toolName => toolName !== name));
        } else {
            // 如果当前是启用状态，则禁用（添加到禁用列表）
            setDisabledTools([...disabledTools, name]);
        }
    };

    return <ToolGroupContainer>
        <GroupHeader $expanded={expanded} onClick={() => setExpanded(!expanded)}>
            <GroupHeaderLeft>
                {expanded ? <BsChevronDown /> : <BsChevronRight />}
                <Form.Check
                    type='checkbox'
                    checked={enabledCount === tools.length}
                    ref={(el: HTMLInputElement | null) => {
                        if (el) {
                            el.indeterminate = enabledCount > 0 && enabledCount < tools.length;
                        }
                    }}
                    onClick={(e) => e.stopPropagation()}
                    onChange={(e) => {
                        e.stopPropagation();
                        toggleAllTools(e.target.checked);
                    }}
                    title={enabledCount === tools.length ? '全部关闭' : enabledCount === 0 ? '全部开启' : '全部开启'}
                />
                <GroupTitle>内置工具</GroupTitle>
                <ToolCount>({enabledCount}/{tools.length})</ToolCount>
            </GroupHeaderLeft>
        </GroupHeader>
        {expanded && (
            <ToolsList>
                {tools.map(tool => (
                    <ToolItem key={tool.name}>
                        <ToolToggle>
                            <Form.Check
                                type='checkbox'
                                checked={!disabledTools.includes(`${tool.plugin}.${tool.name}`)}
                                onChange={() => toggleTool(`${tool.plugin}.${tool.name}`)}
                            />
                        </ToolToggle>
                        <ToolInfo>
                            <ToolName>{tool.title}</ToolName>
                            <ToolDescription>{tool.description}</ToolDescription>
                        </ToolInfo>
                    </ToolItem>
                ))}
            </ToolsList>
        )}
    </ToolGroupContainer>;
};

const McpServerItem = ({ server }: { server: McpServer }) => {
    const { deleteMcpServer, getMcpTools } = useContext();
    const [expanded, setExpanded] = useState(false);
    const [disabledTools, setDisabledTools] = useLocalStorageStateWithBook<string[]>(`mcp-${server.name}-disabled-tools`, []);

    const { result: tools = [], loading } = useAsync(() => {
        return getMcpTools(server);
    }, [server]);

    const enabledCount = tools.length - disabledTools.length;

    const toggleAllTools = (enabled: boolean) => {
        if (enabled) {
            // 启用所有工具，清空禁用列表
            setDisabledTools([]);
        } else {
            // 禁用所有工具，将所有工具名称添加到禁用列表
            setDisabledTools(tools.map(tool => tool.name));
        }
    };

    const toggleTool = (name: string) => {
        if (disabledTools.includes(name)) {
            // 如果当前是禁用状态，则启用（从禁用列表中移除）
            setDisabledTools(disabledTools.filter(toolName => toolName !== name));
        } else {
            // 如果当前是启用状态，则禁用（添加到禁用列表）
            setDisabledTools([...disabledTools, name]);
        }
    };

    return (
        <ToolGroupContainer key={server.name}>
            <GroupHeader $expanded={expanded} onClick={() => setExpanded(!expanded)}>
                <GroupHeaderLeft>
                    {expanded ? <BsChevronDown /> : <BsChevronRight />}
                    <Form.Check
                        type='checkbox'
                        checked={enabledCount === tools.length}
                        ref={(el: HTMLInputElement | null) => {
                            if (el) {
                                el.indeterminate = enabledCount > 0 && enabledCount < tools.length;
                            }
                        }}
                        onClick={(e) => e.stopPropagation()}
                        onChange={(e) => {
                            e.stopPropagation();
                            toggleAllTools(e.target.checked);
                        }}
                        title={enabledCount === tools.length ? '全部关闭' : enabledCount === 0 ? '全部开启' : '全部开启'}
                    />
                    <GroupTitle>{server.name}</GroupTitle>
                    <ToolCount>({enabledCount}/{tools.length})</ToolCount>
                </GroupHeaderLeft>
                <GroupActions onClick={e => e.stopPropagation()}>
                    <DeleteButton
                        onClick={() => {
                            deleteMcpServer(server.name);
                        }}
                        title='删除服务器'
                    >
                        <BsTrash />
                    </DeleteButton>
                </GroupActions>
            </GroupHeader>

            {expanded && <ToolsList>
                {loading ? <Loader /> : tools.map(tool => (
                    <ToolItem key={tool.name}>
                        <ToolToggle>
                            <Form.Check
                                type='checkbox'
                                checked={!disabledTools.includes(tool.name)}
                                onChange={() => toggleTool(tool.name)}
                            />
                        </ToolToggle>
                        <ToolInfo>
                            <ToolName>{tool.title || tool.name}</ToolName>
                            <ToolDescription>{tool.description}</ToolDescription>
                        </ToolInfo>
                    </ToolItem>
                ))}
            </ToolsList>}
        </ToolGroupContainer>
    );
};

// 添加 MCP 服务器弹窗组件
const AddMcpModal = ({ show, onHide, onAdd }: {
    show: boolean;
    onHide: () => void;
    onAdd: (server: McpServer) => void;
}) => {
    const [serverUrl, setServerUrl] = useState('');
    const [serverName, setServerName] = useState('');

    const handleAdd = async () => {
        if (serverName && serverUrl) {
            await onAdd({ name: serverName, url: serverUrl });
            setServerUrl('');
            setServerName('');
            onHide();
        }
    };

    return (
        <Modal show={show} onHide={onHide}>
            <Modal.Header closeButton>
                <Modal.Title as='h6'>添加 MCP 服务器</Modal.Title>
            </Modal.Header>
            <Modal.Body>
                <div className='mb-3'>
                    <label className='form-label'>服务器名称</label>
                    <input
                        type='text'
                        className='form-control'
                        value={serverName}
                        onChange={e => setServerName(e.target.value)}
                        placeholder='输入服务器名称'
                    />
                </div>
                <div className='mb-3'>
                    <label className='form-label'>服务器地址</label>
                    <input
                        type='text'
                        className='form-control'
                        value={serverUrl}
                        onChange={e => setServerUrl(e.target.value)}
                        placeholder='输入服务器 URL 或命令'
                    />
                </div>
            </Modal.Body>
            <Modal.Footer>
                <Button variant='secondary' onClick={onHide}>
                    取消
                </Button>
                <Button
                    onClick={handleAdd}
                    disabled={!serverName || !serverUrl}
                >
                    添加
                </Button>
            </Modal.Footer>
        </Modal>
    );
};

const ToolsContainer = styled.div`
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
`;

const ToolGroupContainer = styled.div`
    border: 1px solid var(--bs-border-color);
    border-radius: var(--bs-border-radius);
    overflow: hidden;
`;

const GroupHeader = styled.div<{ $expanded?: boolean }>`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.375rem 0.5rem;
    background: var(--ttw-foreground);
    cursor: pointer;
    border-bottom: ${props => props.$expanded ? '1px solid var(--bs-border-color)' : 'none'};

    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const GroupHeaderLeft = styled.div`
    display: flex;
    align-items: center;
    gap: 0.375rem;
`;

const GroupTitle = styled.div`
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
`;

const ToolCount = styled.span`
    color: var(--bs-secondary);
    font-size: 1rem;
`;

const GroupActions = styled.div`
    display: flex;
    align-items: center;
`;

const ToolsList = styled.div`
    position: relative;
    padding: 0.125rem 0.25rem 0.25rem;
`;

const ToolItem = styled.div`
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.25rem 0.5rem;
    border-radius: var(--bs-border-radius);

    &:hover {
        background: var(--ttw-box-hover-background);
    }
`;

const ToolInfo = styled.div`
    flex: 1;
    display: flex;
    align-items: center;
    gap: 0.5rem;
`;

const ToolName = styled.div`
    font-weight: 500;
    width: 180px;
    font-size: 1rem;
    flex-shrink: 0;
`;

const ToolDescription = styled.div`
    font-size: 1rem;
    color: var(--bs-secondary);
    flex: 1;
`;

const ToolToggle = styled.div`
    margin-right: 0.5rem;
    display: flex;
    align-items: center;
`;

const DeleteButton = styled.button`
    background: none;
    border: none;
    color: var(--bs-secondary);
    cursor: pointer;
    padding: 0.25rem;
    margin-left: 0.5rem;
    border-radius: var(--bs-border-radius);
    display: flex;
    align-items: center;

    &:hover {
        color: var(--bs-danger);
        background: var(--ttw-box-hover-background);
    }
`;
